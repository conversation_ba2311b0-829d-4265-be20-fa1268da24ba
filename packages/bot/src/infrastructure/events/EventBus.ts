/*
 * Copyright (C) 2025 InterChat
 *
 * InterChat is free software: you can redistribute it and/or modify
 * it under the terms of the GNU Affero General Public License as published
 * by the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 *
 * InterChat is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * GNU Affero General Public License for more details.
 *
 * You should have received a copy of the GNU Affero General Public License
 * along with InterChat.  If not, see <https://www.gnu.org/licenses/>.
 */

import { injectable, inject } from 'inversify';
import { TYPES } from '../../shared/types/TYPES.js';
import { Logger } from '../../shared/utils/Logger.js';

/**
 * Base Domain Event
 *
 * All domain events should extend from this base class.
 * In a multi-cluster environment, events can be local to a cluster
 * or broadcast across all clusters.
 */
export abstract class DomainEvent {
  public readonly occurredAt: Date;
  public readonly eventId: string;
  public readonly clusterId: string;

  constructor(
    public readonly type: string,
    public readonly aggregateId: string,
    public readonly version: number = 1,
    public readonly shouldBroadcast: boolean = false, // Whether to send to other clusters
    clusterId?: string
  ) {
    this.occurredAt = new Date();
    this.eventId = crypto.randomUUID();
    this.clusterId = clusterId || process.env.CLUSTER_ID || '0';
  }

  toJSON(): Record<string, any> {
    return {
      eventId: this.eventId,
      type: this.type,
      aggregateId: this.aggregateId,
      version: this.version,
      clusterId: this.clusterId,
      shouldBroadcast: this.shouldBroadcast,
      occurredAt: this.occurredAt.toISOString(),
      data: this.getData(),
    };
  }

  static fromJSON(_json: Record<string, any>): DomainEvent {
    // To be implemented by each concrete event class
    throw new Error('fromJSON must be implemented by concrete event classes');
  }

  protected abstract getData(): Record<string, any>;
}

/**
 * Event Handler Interface
 */
export interface IEventHandler<T extends DomainEvent = DomainEvent> {
  handle(event: T): Promise<void>;
  readonly eventType: string;
  readonly handlerId: string;
}

/**
 * Event Bus Interface
 *
 * Handles both local (intra-cluster) and distributed (inter-cluster) events
 */
export interface IEventBus {
  // Publishing events
  publish<T extends DomainEvent>(event: T): Promise<void>;
  publishMany<T extends DomainEvent>(events: T[]): Promise<void>;

  // Local cluster subscriptions
  subscribe<T extends DomainEvent>(eventType: string, handler: IEventHandler<T>): void;
  unsubscribe<T extends DomainEvent>(eventType: string, handler: IEventHandler<T>): void;

  // Cluster management
  getClusterId(): string;
  isHealthy(): Promise<boolean>;

  // Event history and debugging
  getLocalEventHistory(): DomainEvent[];
  clearLocalHistory(): void;
}

/**
 * Event Subscription
 */
interface EventSubscription {
  handler: IEventHandler;
  once?: boolean;
  clusterId?: string; // Which cluster this subscription belongs to
}

/**
 * Cluster-Aware Event Bus Implementation
 *
 * This handles both local events within a cluster and distributed events
 * across clusters using Redis as the transport mechanism.
 */
@injectable()
export class ClusterEventBus implements IEventBus {
  private localSubscriptions = new Map<string, EventSubscription[]>();
  private localEventHistory: DomainEvent[] = [];
  private readonly maxHistorySize = 1000;
  private readonly clusterId: string;

  // Redis client will be injected for distributed events
  private redisClient?: any; // Will be properly typed when we add Redis

  constructor(@inject(TYPES.ClusterId) clusterId: string) {
    this.clusterId = clusterId;
  }

  /**
   * Set Redis client for distributed event broadcasting
   */
  setRedisClient(redisClient: any): void {
    this.redisClient = redisClient;
    this.setupRedisSubscriptions();
  }

  async publish<T extends DomainEvent>(event: T): Promise<void> {
    Logger.info(`[EventBus] Publishing event: ${event.type} - ${event.eventId}`);
    // Add to local event history
    this.addToLocalHistory(event);

    // Handle local subscriptions first
    await this.publishToLocalSubscriptions(event);

    // If event should be broadcast and we have Redis, send to other clusters
    if (event.shouldBroadcast && this.redisClient) {
      await this.publishToOtherClusters(event);
    }
  }

  async publishMany<T extends DomainEvent>(events: T[]): Promise<void> {
    // Process all events in parallel for better performance
    const promises = events.map(event => this.publish(event));
    await Promise.all(promises);
  }

  subscribe<T extends DomainEvent>(
    eventType: string,
    handler: IEventHandler<T>,
    options?: { once?: boolean }
  ): void {
    if (!this.localSubscriptions.has(eventType)) {
      this.localSubscriptions.set(eventType, []);
    }

    this.localSubscriptions.get(eventType)!.push({
      handler: handler as IEventHandler,
      once: options?.once,
      clusterId: this.clusterId,
    });
  }

  unsubscribe<T extends DomainEvent>(
    eventType: string,
    handler: IEventHandler<T>
  ): void {
    const handlers = this.localSubscriptions.get(eventType);
    if (!handlers) return;

    const filteredHandlers = handlers.filter(
      subscription => subscription.handler !== handler
    );

    if (filteredHandlers.length === 0) {
      this.localSubscriptions.delete(eventType);
    } else {
      this.localSubscriptions.set(eventType, filteredHandlers);
    }
  }

  /**
   * Subscribe to an event only once
   */
  once<T extends DomainEvent>(
    eventType: string,
    handler: IEventHandler<T>
  ): void {
    this.subscribe(eventType, handler, { once: true });
  }

  getClusterId(): string {
    return this.clusterId;
  }

  async isHealthy(): Promise<boolean> {
    try {
      // Check if Redis connection is healthy (if we're using distributed events)
      if (this.redisClient) {
        await this.redisClient.ping();
      }
      return true;
    } catch (error) {
      Logger.error('EventBus health check failed:', error);
      return false;
    }
  }

  getLocalEventHistory(): DomainEvent[] {
    return [...this.localEventHistory];
  }

  clearLocalHistory(): void {
    this.localEventHistory = [];
  }

  /**
   * Get all local handlers for an event type
   */
  getLocalHandlers(eventType: string): IEventHandler[] {
    return (this.localSubscriptions.get(eventType) || [])
      .map(subscription => subscription.handler);
  }

  /**
   * Clear all local subscriptions
   */
  clearLocalSubscriptions(): void {
    this.localSubscriptions.clear();
  }

  /**
   * Handle events from other clusters (received via Redis)
   */
  private async handleDistributedEvent(eventData: Record<string, any>): Promise<void> {
    // Don't process events from our own cluster
    if (eventData.clusterId === this.clusterId) {
      return;
    }

    try {
      // Reconstruct the event object
      const event = this.deserializeEvent(eventData);

      // Process through local subscriptions only
      await this.publishToLocalSubscriptions(event);

    } catch (error) {
      Logger.error('Error handling distributed event:', error, eventData);
    }
  }

  /**
   * Publish event to local subscriptions within this cluster
   */
  private async publishToLocalSubscriptions<T extends DomainEvent>(event: T): Promise<void> {
    const handlers = this.localSubscriptions.get(event.type) || [];

    // Execute all handlers in parallel
    const promises = handlers.map(subscription =>
      this.executeHandler(subscription.handler, event)
    );

    // Remove one-time handlers
    this.localSubscriptions.set(
      event.type,
      handlers.filter(subscription => !subscription.once)
    );

    await Promise.all(promises);
  }

  /**
   * Broadcast event to other clusters via Redis
   */
  private async publishToOtherClusters<T extends DomainEvent>(event: T): Promise<void> {
    if (!this.redisClient) return;

    try {
      const channel = `interchat:events:${event.type}`;
      const payload = JSON.stringify(event.toJSON());

      await this.redisClient.publish(channel, payload);
    } catch (error) {
      Logger.error('Error publishing event to other clusters:', error);
      // Don't throw - local event processing should continue
    }
  }

  /**
   * Set up Redis subscriptions for distributed events
   */
  private setupRedisSubscriptions(): void {
    if (!this.redisClient) return;

    // Subscribe to all event channels
    const pattern = 'interchat:events:*';

    this.redisClient.psubscribe(pattern);
    this.redisClient.on('pmessage', async (_pattern: string, channel: string, message: string) => {
      try {
        const eventData = JSON.parse(message);
        await this.handleDistributedEvent(eventData);
      } catch (error) {
        Logger.error('Error processing Redis event message:', error);
      }
    });
  }

  /**
   * Execute a single event handler with error handling
   */
  private async executeHandler(
    handler: IEventHandler,
    event: DomainEvent
  ): Promise<void> {
    try {
      await handler.handle(event);
    } catch (error) {
      Logger.error(
        `Error in event handler ${handler.handlerId} for event ${event.type}:`,
        error
      );

      // In production, you might want to:
      // 1. Publish a FailedEventHandler event
      // 2. Send to dead letter queue
      // 3. Alert monitoring systems
    }
  }

  /**
   * Add event to local history
   */
  private addToLocalHistory(event: DomainEvent): void {
    this.localEventHistory.push(event);

    // Keep history size manageable
    if (this.localEventHistory.length > this.maxHistorySize) {
      this.localEventHistory = this.localEventHistory.slice(-this.maxHistorySize);
    }
  }

  /**
   * Deserialize event from JSON
   *
   * In a real implementation, you'd have a proper event registry
   * that maps event types to their corresponding classes
   */
  private deserializeEvent(eventData: Record<string, any>): DomainEvent {
    // This is a simplified implementation
    // In practice, you'd have an EventRegistry that knows how to
    // reconstruct each specific event type

    class GenericDistributedEvent extends DomainEvent {
      private eventData: Record<string, any>;

      constructor(data: Record<string, any>) {
        super(data.type, data.aggregateId, data.version, false);
        this.eventData = data.data;
        Object.assign(this, {
          eventId: data.eventId,
          clusterId: data.clusterId,
          occurredAt: new Date(data.occurredAt)
        });
      }

      protected getData(): Record<string, any> {
        return this.eventData;
      }
    }

    return new GenericDistributedEvent(eventData);
  }
}
