/**
 * Copyright (c) 2024 InterChat
 *
 * This file is part of InterChat, licensed under the AGPL-3.0 license.
 * See the LICENSE file in the root directory for license information.
 */

/**
 * Unified Context Interface
 *
 * Provides a common interface for both slash commands and prefix commands
 * to work with the flexible command architecture.
 */

import type { User, Guild, GuildMember, Message, EmbedBuilder } from 'discord.js';

/**
 * Unified context interface for commands
 */
export interface UnifiedContext {
  // Basic properties
  readonly user: User;
  readonly guild: Guild | null;
  readonly member: GuildMember | null;
  readonly channel: any;

  // State properties
  readonly replied: boolean;
  readonly deferred: boolean;

  // Response methods
  reply(options: any): Promise<Message>;
  editReply(options: any): Promise<Message>;
  followUp(options: any): Promise<Message>;
  deferReply(options?: any): Promise<any>;

  // Option parsing methods
  getString(name: string | number): string | null;
  getInteger(name: string | number): number | null;
  getNumber(name: string | number): number | null;
  getBoolean(name: string | number): boolean | null;
  getUser(name: string | number): Promise<User | null> | User | null;
  getChannel(name: string | number): any;
  getRole(name: string | number): any;
  getSubcommand(): string | null;

  // Utility methods
  checkPermissions?(permissions: string[]): Promise<boolean>;
}

/**
 * Context adapter for InteractionContext
 */
export class InteractionContextAdapter implements UnifiedContext {
  constructor(private context: any) {}

  get user(): User { return this.context.user; }
  get guild(): Guild | null { return this.context.guild; }
  get member(): GuildMember | null { return this.context.member; }
  get channel(): any { return this.context.channel; }
  get replied(): boolean { return this.context.replied; }
  get deferred(): boolean { return this.context.deferred; }

  async reply(options: any): Promise<Message> { return await this.context.reply(options); }
  async editReply(options: any): Promise<Message> { return await this.context.editReply(options); }
  async followUp(options: any): Promise<Message> { return await this.context.followUp(options); }
  async deferReply(options?: any): Promise<any> { return await this.context.deferReply(options); }

  getString(name: string | number): string | null { return this.context.options.getString(name as string); }
  getInteger(name: string | number): number | null { return this.context.options.getInteger(name as string); }
  getNumber(name: string | number): number | null { return this.context.options.getNumber(name as string); }
  getBoolean(name: string | number): boolean | null { return this.context.options.getBoolean(name as string); }
  getUser(name: string | number): User | null { return this.context.options.getUser(name as string); }
  getChannel(name: string | number): any { return this.context.options.getChannel(name as string); }
  getRole(name: string | number): any { return this.context.options.getRole(name as string); }
  getSubcommand(): string | null { return this.context.options.getSubcommand(); }

  async checkPermissions(permissions: string[]): Promise<boolean> {
    return this.context.checkPermissions ? await this.context.checkPermissions(permissions) : true;
  }
}

/**
 * Context adapter for PrefixContext
 */
export class PrefixContextAdapter implements UnifiedContext {
  constructor(private context: any) {}

  get user(): User { return this.context.user; }
  get guild(): Guild | null { return this.context.guild; }
  get member(): GuildMember | null { return this.context.member; }
  get channel(): any { return this.context.channel; }
  get replied(): boolean { return this.context.replied; }
  get deferred(): boolean { return this.context.deferred; }

  async reply(options: any): Promise<Message> { return await this.context.reply(options); }
  async editReply(options: any): Promise<Message> { return await this.context.editReply(options); }
  async followUp(options: any): Promise<Message> { return await this.context.followUp(options); }
  async deferReply(options?: any): Promise<any> { return await this.context.deferReply(options); }

  getString(name: string | number): string | null {
    return typeof name === 'number' ? this.context.getString(name) : this.context.args[0] || null;
  }
  getInteger(name: string | number): number | null {
    return typeof name === 'number' ? this.context.getInteger(name) : null;
  }
  getNumber(name: string | number): number | null {
    return typeof name === 'number' ? this.context.getNumber(name) : null;
  }
  getBoolean(name: string | number): boolean | null {
    return typeof name === 'number' ? this.context.getBoolean(name) : null;
  }
  async getUser(name: string | number): Promise<User | null> {
    return typeof name === 'number' ? await this.context.getUser(name) : null;
  }
  getChannel(name: string | number): any {
    return typeof name === 'number' ? this.context.getChannel(name) : null;
  }
  getRole(name: string | number): any {
    return typeof name === 'number' ? this.context.getRole(name) : null;
  }
  getSubcommand(): string | null { return this.context.getSubcommand(); }

  async checkPermissions(permissions: string[]): Promise<boolean> {
    return this.context.checkPermissions ? await this.context.checkPermissions(permissions) : true;
  }
}

/**
 * Create a unified context from either InteractionContext or PrefixContext
 */
export function createUnifiedContext(context: any): UnifiedContext {
  if (context.constructor.name === 'PrefixContext') {
    return new PrefixContextAdapter(context);
  }
  else {
    return new InteractionContextAdapter(context);
  }
}
